# Traditional Chinese translation
# Copyright (C) 2016
# This file is distributed under the same license as the mptt package.
# Translators:
# <AUTHOR> <EMAIL> 2016.
#
msgid ""
msgstr ""
"Project-Id-Version: 0.8.6\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2016-11-08 18:24+0800\n"
"PO-Revision-Date: 2019-05-17 08:58+0800\n"
"Last-Translator: \n"
"Language-Team: zh-<PERSON><PERSON> <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#, python-format
msgid "Successfully deleted %(count)d items."
msgstr "成功刪除 %(count)d 项"

#, python-format
msgid "Delete selected %(verbose_name_plural)s"
msgstr "刪除已选: %(verbose_name_plural)s "

msgid "title"
msgstr "标题"

msgid "Did not understand moving instruction."
msgstr "无法执行移动"

msgid "Objects have disappeared, try again."
msgstr "对象已消失，请再试一次"

msgid "No permission"
msgstr "没有权限"

#, python-format
msgid "Database error: %s"
msgstr "数据库错误: %s"

#, python-format
msgid "%s has been successfully moved."
msgstr "已成功移动 %s"

msgid "move node before node"
msgstr "往前移"

msgid "move node to child position"
msgstr "移至下一层"

msgid "move node after node"
msgstr "往后移"

msgid "Collapse tree"
msgstr "收起"

msgid "Expand tree"
msgstr "展开"

msgid "All"
msgstr "全部"

msgid "First child"
msgstr "第一子节点"

msgid "Last child"
msgstr "最后子节点"

msgid "Left sibling"
msgstr "同层左节点"

msgid "Right sibling"
msgstr "同层右节点"

msgid "Invalid parent"
msgstr "无效的父节点"

msgid "Cannot insert a node which has already been saved."
msgstr "无法加入已存在的节点"

#, python-format
msgid "An invalid position was given: %s."
msgstr "无效的位置: %s"

msgid "A node may not be made a sibling of itself."
msgstr "节点无法指定自己成为自己同层节点"

msgid "A node may not be made a child of itself."
msgstr "节点无法指定自己成为自己的子节点"

msgid "A node may not be made a child of any of its descendants."
msgstr "节点无法指定自己成为子节点的子节点"

msgid "A node may not be made a sibling of any of its descendants."
msgstr "节点无法指定自己成为子节点的同层节点"

msgid "register() expects a Django model class argument"
msgstr "register() 需要一个Django模型类別的参数"

#, python-format
msgid " By %(filter_title)s "
msgstr " %(filter_title)s "

#, python-format
msgid "full_tree_for_model tag was given an invalid model: %s"
msgstr "full_tree_for_model 标签被指定到无效的数据库模型: %s"

#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model: %s"
msgstr "drilldoen_tree_for_node 标签被指定到无效的数据库模型: %s"

#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model field: %s"
msgstr "drilldoen_tree_for_node 标签被指定到无效的字段: %s"

#, python-format
msgid "%s tag requires three arguments"
msgstr "%s 标签需要三個参数"

#, python-format
msgid "second argument to %s tag must be 'as'"
msgstr "%s 标签的第二个参数需为'as'"

#, python-format
msgid "%s tag requires either three, seven or eight arguments"
msgstr "%s 标签需要3或7或8個参数"

#, python-format
msgid "if seven arguments are given, fourth argument to %s tag must be 'with'"
msgstr "%s 标签传入7個参数时，第4个需为'with'"

#, python-format
msgid "if seven arguments are given, sixth argument to %s tag must be 'in'"
msgstr "%s 标签传入7个参数时，第6个需为'in'"

#, python-format
msgid "if eight arguments are given, fourth argument to %s tag must be 'cumulative'"
msgstr "%s 标签传入8个参数时，第4个需为'cumulative'"

#, python-format
msgid "if eight arguments are given, fifth argument to %s tag must be 'count'"
msgstr "%s 标签传入8个参数时，第5个需为'count'"

#, python-format
msgid "if eight arguments are given, seventh argument to %s tag must be 'in'"
msgstr "%s 标签传入8个参数时，第7个需为'in'"

#, python-format
msgid "%s tag requires a queryset"
msgstr "%s 标签需要一个queryset"

#, python-format
msgid "Node %s not in depth-first order"
msgstr "节点 %s 不在深度优先次序中"
