# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2014-08-19 16:30-300\n"
"PO-Revision-Date: 2014-08-19 16:30-300\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: __init__.py:34
#, python-format
msgid "The model %s has already been registered."
msgstr "O modelo %s foi registrado."

#: forms.py:41
msgid "First child"
msgstr "Primeiro filho"

#: forms.py:42
msgid "Last child"
msgstr "Último filho"

#: forms.py:43
msgid "Left sibling"
msgstr "Irmão à esquerda"

#: forms.py:44
msgid "Right sibling"
msgstr "Irmão à direita"

#: managers.py:121
msgid "Cannot insert a node which has already been saved."
msgstr "Não é possível inserir um nó que já foi salvo."

#: managers.py:306 managers.py:480 managers.py:516 managers.py:673
#, python-format
msgid "An invalid position was given: %s."
msgstr "Posição inválida: %s."

#: managers.py:466 managers.py:653
msgid "A node may not be made a sibling of itself."
msgstr "Um nó não pode ser irmão de si mesmo."

#: managers.py:632 managers.py:753
msgid "A node may not be made a child of itself."
msgstr "Um nó não pode ser filho de si mesmo."

#: managers.py:634 managers.py:755
msgid "A node may not be made a child of any of its descendants."
msgstr "Um nó não pode ser filho de nenhum de seus descendentes."

#: managers.py:655
msgid "A node may not be made a sibling of any of its descendants."
msgstr "Um nó não pode ser irmão de nenhum de seus descendentes."

#: templatetags/mptt_tags.py:23
#, python-format
msgid "full_tree_for_model tag was given an invalid model: %s"
msgstr "full_tree_for_model recebeu um modelo inválido: %s"

#: templatetags/mptt_tags.py:44
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model: %s"
msgstr "drilldown_tree_for_node recebeu um modelo inválido: %s"

#: templatetags/mptt_tags.py:48
#, python-format
msgid "drilldown_tree_for_node tag was given an invalid model field: %s"
msgstr "drilldown_tree_for_node recebeu um modelo inválido: %s"

#: templatetags/mptt_tags.py:72
#, python-format
msgid "%s tag requires three arguments"
msgstr "A tag %s requer três argumentos"

#: templatetags/mptt_tags.py:74 templatetags/mptt_tags.py:125
#, python-format
msgid "second argument to %s tag must be 'as'"
msgstr "o segundo argumento para a tag %s deve ser 'as'"

#: templatetags/mptt_tags.py:123
#, python-format
msgid "%s tag requires either three, seven or eight arguments"
msgstr "A tag %s requer três, sete ou oito argumentos"

#: templatetags/mptt_tags.py:128
#, python-format
msgid "if seven arguments are given, fourth argument to %s tag must be 'with'"
msgstr "se sete argumentos são dados, o quarto argumento para a tag %s deve ser 'with'"

#: templatetags/mptt_tags.py:130
#, python-format
msgid "if seven arguments are given, sixth argument to %s tag must be 'in'"
msgstr "se sete argumentos são dados, o sexto argumento para a tag %s deve ser 'in'"

#: templatetags/mptt_tags.py:134
#, python-format
msgid ""
"if eight arguments are given, fourth argument to %s tag must be 'cumulative'"
msgstr ""
"se oito argumentos são dados, o quarto argumento para a tag %s deve ser 'cumulative'"

#: templatetags/mptt_tags.py:136
#, python-format
msgid "if eight arguments are given, fifth argument to %s tag must be 'count'"
msgstr "se oito argumentos são dados, o quinto argumento para a tag %s deve ser 'count'"

#: templatetags/mptt_tags.py:138
#, python-format
msgid "if eight arguments are given, seventh argument to %s tag must be 'in'"
msgstr "se oito argumentos são dados, o sétimo argumento para a tag %s deve ser 'in'"
