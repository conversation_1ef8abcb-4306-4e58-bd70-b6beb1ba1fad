from django.contrib import admin
from captcha.models import CaptchaStore


@admin.register(CaptchaStore)
class CaptchaStoreAdmin(admin.ModelAdmin):
    list_display = ['hashkey', 'challenge', 'response', 'expiration', 'is_expired']
    list_filter = ['expiration']
    search_fields = ['hashkey', 'challenge', 'response']
    readonly_fields = ['hashkey', 'challenge', 'response', 'expiration', 'is_expired']
    date_hierarchy = 'expiration'
    ordering = ['-expiration']
    
    def is_expired(self, obj):
        """检查验证码是否已过期"""
        if obj.expiration is None:
            return None
        from django.utils import timezone
        return obj.expiration < timezone.now()
    is_expired.boolean = True
    is_expired.short_description = '是否过期'
    
    def has_add_permission(self, request):
        """禁用添加权限，验证码应该由系统自动生成"""
        return False
    
    def has_change_permission(self, request, obj=None):
        """禁用修改权限，验证码不应该被手动修改"""
        return False
    
    class Meta:
        verbose_name = "验证码"
        verbose_name_plural = "验证码管理"
