from captcha.models import CaptchaStore

from django.contrib.auth.backends import ModelBackend
from django.contrib.auth.models import User
from django.db.models import Q


def code_check(captcha_key, captcha_value):
    """
    验证码校验
    """
    if not captcha_key or not captcha_value:
        return {"status": False, "msg": "验证码不能为空"}
    
    try:
        # 获取验证码
        captcha = CaptchaStore.objects.get(hashkey=captcha_key)
        if captcha.response.lower() != captcha_value.lower():
            return {"status": False, "msg": "验证码错误"}
        
        # 使用后清除
        captcha.delete()
        return {"status": True, "msg": "验证通过"}
    except CaptchaStore.DoesNotExist:
        return {"status": False, "msg": "验证码已过期"}
    except Exception as e:
        return {"status": False, "msg": f"验证失败: {str(e)}"}


class CustomBackend(ModelBackend):
    """
    自定义用户认证后端
    
    允许使用用户名或邮箱登录
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # 使用用户名或邮箱查询用户
            user = User.objects.get(Q(username=username) | Q(email=username))
            
            # 检查密码
            if user.check_password(password):
                return user
        except User.DoesNotExist:
            return None
        except Exception:
            return None
        
        return None
