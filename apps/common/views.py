from django.shortcuts import render
from django_filters.rest_framework import <PERSON>jango<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework import filters
from rest_framework.exceptions import NotFound
from rest_framework.viewsets import GenericViewSet

from apps.common.utils.pagination import PageNumberPaginationUtil
from apps.common.utils.response import Response as Re


# Create your views here.
class CommonMixinViewSet(GenericViewSet):
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    pagination_class = PageNumberPaginationUtil
    ordering = ['create_time']

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        if request.query_params.get('page') and page is not None:
            serializer = self.get_serializer(page, many=True)
            paginated_data = {
                'count': self.paginator.page.paginator.count,
                'next': self.paginator.get_next_link(),
                'previous': self.paginator.get_previous_link(),
                'current': self.paginator.page.number,
                'max_page': self.paginator.page.paginator.num_pages,
                'results': serializer.data
            }
            return Re.success(data=paginated_data)
        serializer = self.get_serializer(queryset, many=True)
        return Re.success(data=serializer.data)

    def retrieve(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Re.success(data=serializer.data)
        except NotFound:
            return Re.not_found()

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        if serializer.is_valid():
            # 将对象保存到数据库中
            self.perform_create(serializer)  # 这里会触发 perform_create 钩子
            instance = serializer.instance  # 获取已创建的实例
            # instance = serializer.save()
            # 用序列化对象返回响应
            return Re.success(data=self.get_serializer(instance).data)
        return Re.bad_request(message="数据验证失败", data=serializer.errors)

    def update(self, request, *args, **kwargs):
        try:
            # Get the object to update
            instance = self.get_object()
        except NotFound:
            return Re.not_found()

        # 使用序列化器验证和更新对象
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        if serializer.is_valid():
            # instance = serializer.save()
            self.perform_update(serializer)  # 这里会触发 perform_update 钩子
            instance = serializer.instance
            # 用更新的对象返回响应
            return Re.success(data=self.get_serializer(instance).data)
        return Re.bad_request(message="数据验证失败", data=serializer.errors)

    def destroy(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
            instance.is_removed = True  # 假删除
            instance.save()
            return Re.success(data=[])
        except NotFound:
            return Re.not_found()

    # 添加这两个关键方法
    def perform_create(self, serializer):
        """提供给子类重写的钩子"""
        serializer.save()

    def perform_update(self, serializer):
        """提供给子类重写的钩子"""
        serializer.save()


METHOD_ACTION = {'get': 'list', "post": "create"}
METHOD_ACTION_SINGLE = {'get': 'retrieve', "put": "update", "delete": "destroy"}
