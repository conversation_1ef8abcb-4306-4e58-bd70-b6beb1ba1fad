from captcha.helpers import captcha_image_url
from captcha.models import CaptchaS<PERSON>
from rest_framework.request import Request
from rest_framework.views import APIView
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView, TokenVerifyView
from rest_framework_simplejwt.tokens import UntypedToken
from rest_framework.exceptions import AuthenticationFailed
from django.contrib.auth.models import User
from rest_framework_simplejwt.authentication import JWTAuthentication

from apps.common.utils.account import code_check
from apps.common.utils.response import Response


class GetCaptcha(APIView):
    authentication_classes = []
    permission_classes = []

    def get(self, request):
        """
        获取验证码
        """
        # 生成新的验证码
        key = CaptchaStore.generate_key()
        image_url = captcha_image_url(key)
        # 获取验证码的值
        # captcha_text = CaptchaStore.objects.get(hashkey=key).challenge
        return Response.success(data={"captcha_key": key, "image_url": image_url})


class LoginTokenObtainPairView(TokenObtainPairView):
    """ 登录视图 """

    def post(self, request: Request, *args, **kwargs):
        code_check_result = code_check(request.data.get("captcha_key"), str(request.data.get("captcha_value")))
        if not code_check_result["status"]:
            return Response.error(message=code_check_result["msg"])
            # 登入
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            print(e.args[0])
            return InvalidToken(e.args[0])
        except AuthenticationFailed as e:
            print(e.args[0])
            return Response.unauthorized(message=e.detail)
        result = serializer.validated_data
        result["username"] = serializer.user.username
        return Response.success(result)


class CustomTokenRefreshView(TokenRefreshView):
    """
    刷新Token视图
    使用统一的响应结构
    """
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            return Response.unauthorized(message="Token无效或已过期")
        except Exception as e:
            return Response.error(message="Token刷新失败")
            
        result = serializer.validated_data
        return Response.success(data=result, message="Token刷新成功")


class CustomTokenVerifyView(TokenVerifyView):
    """
    验证Token视图
    如果是access token验证成功，返回用户数据
    如果是refresh token验证成功，不返回用户数据
    """
    
    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        
        try:
            serializer.is_valid(raise_exception=True)
        except TokenError as e:
            return Response.unauthorized(message="Token无效或已过期")
        except Exception as e:
            return Response.error(message="Token验证失败")
        
        # Token验证成功，现在判断token类型
        token_value = request.data.get('token')
        
        try:
            # 尝试解析token
            untyped_token = UntypedToken(token_value)
            token_type = untyped_token.get('token_type')
            
            response_data = {
                "token_type": token_type,
                "is_valid": True
            }
            
            # 如果是access token，返回用户数据
            if token_type == 'access':
                try:
                    # 使用JWT认证获取用户
                    jwt_auth = JWTAuthentication()
                    validated_token = jwt_auth.get_validated_token(token_value)
                    user = jwt_auth.get_user(validated_token)
                    
                    # 返回基本用户信息
                    user_data = {
                        "id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "first_name": user.first_name,
                        "last_name": user.last_name,
                        "is_active": user.is_active,
                        "is_staff": user.is_staff,
                        "is_superuser": user.is_superuser,
                        "date_joined": user.date_joined.isoformat(),
                        "last_login": user.last_login.isoformat() if user.last_login else None
                    }
                    response_data["user"] = user_data
                    
                except Exception as e:
                    # 如果获取用户信息失败，只返回基本验证信息
                    pass
            
            return Response.success(data=response_data, message="Token验证成功")
            
        except Exception as e:
            # 如果无法解析token类型，返回基本验证成功信息
            return Response.success(data={"is_valid": True}, message="Token验证成功")
