from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView
from django.urls import path

from . import views

drf_patterns = [
    path('token/', views.LoginTokenObtainPairView.as_view(), name='token_obtain_pair'),
    # 刷新Token有效期的接口
    path('token/refresh/', views.CustomTokenRefreshView.as_view(), name='token_refresh'),
    # 验证Token的有效性
    path('token/verify/', views.CustomTokenVerifyView.as_view(), name='token_verify'),

    # 验证码获取
    path('captcha/', views.GetCaptcha.as_view()),

]

urlpatterns = drf_patterns
